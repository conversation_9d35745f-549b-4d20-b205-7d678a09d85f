#!/usr/bin/env python
"""
测试推送提醒统计API接口的脚本
"""

import requests
import json
from datetime import datetime, date, timedelta

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_api_endpoint(endpoint, params=None, description=""):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"URL: {url}")
    if params:
        print(f"参数: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✅ 请求成功")
                    # 打印部分响应数据
                    if 'data' in data:
                        if isinstance(data['data'], dict):
                            for key, value in list(data['data'].items())[:3]:
                                print(f"  {key}: {value}")
                            if len(data['data']) > 3:
                                print("  ...")
                        else:
                            print(f"  数据: {str(data['data'])[:100]}...")
                else:
                    print(f"❌ 请求失败: {data.get('error', '未知错误')}")
            except json.JSONDecodeError:
                print(f"✅ 响应成功 (非JSON格式，可能是文件下载)")
                print(f"  Content-Type: {response.headers.get('Content-Type', 'unknown')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"  响应: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {str(e)}")
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")


def main():
    """主测试函数"""
    print("推送提醒统计API接口测试")
    print("="*60)
    print("请确保Django服务器正在运行 (python manage.py runserver 8000)")
    
    # 获取今天和昨天的日期
    today = date.today()
    yesterday = today - timedelta(days=1)
    week_ago = today - timedelta(days=7)
    
    # 测试各个API接口
    test_cases = [
        {
            'endpoint': '/notification-stats/daily/',
            'params': None,
            'description': '获取今日统计数据'
        },
        {
            'endpoint': '/notification-stats/daily/',
            'params': {'date': today.isoformat()},
            'description': '获取指定日期统计数据'
        },
        {
            'endpoint': '/notification-stats/period/',
            'params': {
                'start_date': week_ago.isoformat(),
                'end_date': today.isoformat()
            },
            'description': '获取最近7天时间段统计'
        },
        {
            'endpoint': '/notification-stats/summary/',
            'params': {'days': 7},
            'description': '获取最近7天汇总统计'
        },
        {
            'endpoint': '/notification-stats/list/',
            'params': {'page_size': 10},
            'description': '获取统计记录列表'
        },
        {
            'endpoint': '/notification-stats/list/',
            'params': {
                'notification_type': 'submitted',
                'page_size': 5
            },
            'description': '获取Submitted类型的统计记录'
        },
        {
            'endpoint': '/notification-stats/list/',
            'params': {
                'receiver_type': 'assignee',
                'page_size': 5
            },
            'description': '获取责任人类型的统计记录'
        },
        {
            'endpoint': '/notification-stats/export/',
            'params': {
                'format': 'json',
                'start_date': week_ago.isoformat(),
                'end_date': today.isoformat()
            },
            'description': '导出JSON格式数据'
        },
        {
            'endpoint': '/notification-stats/export/',
            'params': {
                'format': 'csv',
                'notification_type': 'submitted'
            },
            'description': '导出CSV格式数据'
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        try:
            test_api_endpoint(
                test_case['endpoint'],
                test_case.get('params'),
                test_case['description']
            )
            success_count += 1
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    # 测试仪表板页面
    print(f"\n{'='*60}")
    print("测试: 统计数据仪表板页面")
    dashboard_url = f"{BASE_URL}/notification-stats/"
    print(f"URL: {dashboard_url}")
    
    try:
        response = requests.get(dashboard_url, timeout=10)
        if response.status_code == 200:
            print("✅ 仪表板页面加载成功")
            print(f"  页面大小: {len(response.content)} 字节")
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 页面访问错误: {str(e)}")
    
    # 测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"总测试数: {total_count + 1}")
    print(f"成功数: {success_count}")
    print(f"成功率: {(success_count / total_count) * 100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有API接口测试通过！")
    else:
        print("⚠️  部分接口测试失败，请检查服务器状态")
    
    print(f"\n访问仪表板: {dashboard_url}")
    print("API文档请参考: NOTIFICATION_STATS_README.md")


if __name__ == '__main__':
    main()
