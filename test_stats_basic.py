#!/usr/bin/env python
"""
测试推送提醒统计功能的基础脚本（不依赖JIRA）
"""

import os
import sys
import django
from datetime import datetime, date

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'transsiongroot.settings')
django.setup()

from task_manager.backend.src.tools.notification_stats import NotificationStatsService
from task_manager.models import NotificationStats


def test_basic_stats():
    """
    测试基础统计功能
    """
    print("开始测试推送提醒统计功能...")
    
    # 创建统计服务实例
    stats_service = NotificationStatsService()
    
    # 测试数据
    test_bug_data = [
        {
            'TOS1510-12345': {
                'url': 'http://jira.transsion.com/browse/TOS1510-12345',
                'created': '2025-06-24T10:00:00.000+0800',
                'updated': '2025-06-24T11:00:00.000+0800',
                'flag': True
            }
        },
        {
            'TOS1510-12346': {
                'url': 'http://jira.transsion.com/browse/TOS1510-12346',
                'created': '2025-06-24T10:30:00.000+0800',
                'updated': '2025-06-24T11:30:00.000+0800',
                'flag': False
            }
        }
    ]
    
    # 测试记录推送统计
    print("\n1. 测试记录推送统计...")
    success = stats_service.record_notification(
        notification_type='submitted',
        receiver_type='assignee',
        receiver_name='张三',
        receiver_username='zhangsan',
        bug_data=test_bug_data,
        push_content='{"text":"待审核问题：http://jira.transsion.com/browse/TOS1510-12345 \\n http://jira.transsion.com/browse/TOS1510-12346 \\n "}',
        push_success=True
    )
    print(f"记录推送统计结果: {success}")
    
    # 测试记录TMDE推送统计
    print("\n2. 测试记录TMDE推送统计...")
    success = stats_service.record_notification(
        notification_type='submitted',
        receiver_type='tmde',
        receiver_name='李四',
        receiver_username='lisi',
        tmde_name='李四',
        bug_data=test_bug_data,
        push_content='{"text":"待审核问题已超时，请及时通知owner处理：zhangsan：http://jira.transsion.com/browse/TOS1510-12345 \\n "}',
        push_success=True
    )
    print(f"记录TMDE推送统计结果: {success}")
    
    # 测试记录不同类型的推送
    print("\n3. 测试记录不同类型的推送...")
    
    # Retest问题
    success = stats_service.record_notification(
        notification_type='retest',
        receiver_type='assignee',
        receiver_name='王五',
        receiver_username='wangwu',
        bug_data=[test_bug_data[0]],
        push_content='{"text":"Retest问题：http://jira.transsion.com/browse/TOS1510-12345 \\n "}',
        push_success=True
    )
    print(f"记录Retest推送统计结果: {success}")
    
    # A类问题
    success = stats_service.record_notification(
        notification_type='blocker',
        receiver_type='assignee',
        receiver_name='赵六',
        receiver_username='zhaoliu',
        bug_data=[test_bug_data[1]],
        push_content='{"text":"A类问题审核：http://jira.transsion.com/browse/TOS1510-12346 \\n "}',
        push_success=True
    )
    print(f"记录A类问题推送统计结果: {success}")
    
    # 测试获取今日统计
    print("\n4. 测试获取今日统计...")
    daily_stats = stats_service.get_daily_stats()
    print(f"今日统计数据:")
    print(f"  总推送数: {daily_stats['total_notifications']}")
    print(f"  总Bug数: {daily_stats['total_bugs']}")
    print(f"  按类型统计: {daily_stats['by_type']}")
    print(f"  按接收人类型统计: {daily_stats['by_receiver_type']}")
    
    # 测试获取时间段统计
    print("\n5. 测试获取时间段统计...")
    today = date.today()
    period_stats = stats_service.get_period_stats(today, today)
    print(f"时间段统计数据:")
    print(f"  时间段: {period_stats['period']}")
    print(f"  总推送数: {period_stats['total_notifications']}")
    print(f"  总Bug数: {period_stats['total_bugs']}")
    
    # 查询数据库中的记录
    print("\n6. 查询数据库中的统计记录...")
    records = NotificationStats.objects.all().order_by('-created_at')[:10]
    print(f"数据库中共有 {NotificationStats.objects.count()} 条记录")
    print("最近的记录:")
    for i, record in enumerate(records, 1):
        print(f"  {i}. ID: {record.id}, 类型: {record.get_notification_type_display()}, "
              f"接收人: {record.receiver_name}, Bug数量: {record.bug_count}, "
              f"推送日期: {record.push_date}, 创建时间: {record.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n基础功能测试完成！")


def test_assignee_notification():
    """
    测试责任人推送统计
    """
    print("\n测试责任人推送统计功能...")
    
    stats_service = NotificationStatsService()
    
    # 模拟责任人数据
    assignee_data = {
        'zhangsan': [
            {
                'TOS1510-11111': {
                    'url': 'http://jira.transsion.com/browse/TOS1510-11111',
                    'created': '2025-06-24T09:00:00.000+0800',
                    'updated': '2025-06-24T10:00:00.000+0800',
                    'flag': True
                }
            },
            {
                'TOS1510-11112': {
                    'url': 'http://jira.transsion.com/browse/TOS1510-11112',
                    'created': '2025-06-24T09:30:00.000+0800',
                    'updated': '2025-06-24T10:30:00.000+0800',
                    'flag': False
                }
            }
        ],
        'lisi': [
            {
                'TOS1510-11113': {
                    'url': 'http://jira.transsion.com/browse/TOS1510-11113',
                    'created': '2025-06-24T08:00:00.000+0800',
                    'updated': '2025-06-24T09:00:00.000+0800',
                    'flag': True
                }
            }
        ]
    }
    
    # 记录责任人推送统计
    record_ids = stats_service.record_assignee_notification(
        notification_type='submitted',
        assignee_data=assignee_data,
        prompt='待审核问题'
    )
    print(f"创建的责任人统计记录ID: {record_ids}")


def test_tmde_notification():
    """
    测试TMDE推送统计
    """
    print("\n测试TMDE推送统计功能...")
    
    stats_service = NotificationStatsService()
    
    # 模拟TMDE数据
    tmde_data = {
        'cheng.chen': {
            'zhangsan': [
                {
                    'TOS1510-22221': {
                        'url': 'http://jira.transsion.com/browse/TOS1510-22221',
                        'created': '2025-06-24T07:00:00.000+0800',
                        'updated': '2025-06-24T08:00:00.000+0800',
                        'flag': True
                    }
                }
            ],
            'lisi': [
                {
                    'TOS1510-22222': {
                        'url': 'http://jira.transsion.com/browse/TOS1510-22222',
                        'created': '2025-06-24T07:30:00.000+0800',
                        'updated': '2025-06-24T08:30:00.000+0800',
                        'flag': True
                    }
                }
            ]
        },
        'xiaoqin.bao': {
            'wangwu': [
                {
                    'TOS1510-22223': {
                        'url': 'http://jira.transsion.com/browse/TOS1510-22223',
                        'created': '2025-06-24T06:00:00.000+0800',
                        'updated': '2025-06-24T07:00:00.000+0800',
                        'flag': True
                    }
                }
            ]
        }
    }
    
    # 记录TMDE推送统计
    record_ids = stats_service.record_tmde_notification(
        notification_type='submitted',
        tmde_data=tmde_data,
        prompt='待审核问题'
    )
    print(f"创建的TMDE统计记录ID: {record_ids}")


def show_final_summary():
    """
    显示最终统计汇总
    """
    print("\n" + "=" * 60)
    print("推送提醒统计数据汇总")
    print("=" * 60)
    
    # 总记录数
    total_records = NotificationStats.objects.count()
    total_bugs = sum(record.bug_count for record in NotificationStats.objects.all())
    print(f"总推送记录数: {total_records}")
    print(f"总Bug数量: {total_bugs}")
    
    # 按推送类型统计
    print("\n按推送类型统计:")
    for notification_type, display_name in NotificationStats.NOTIFICATION_TYPE_CHOICES:
        records = NotificationStats.objects.filter(notification_type=notification_type)
        count = records.count()
        bugs = sum(record.bug_count for record in records)
        print(f"  {display_name}: {count} 条记录, {bugs} 个Bug")
    
    # 按接收人类型统计
    print("\n按接收人类型统计:")
    for receiver_type, display_name in NotificationStats.RECEIVER_TYPE_CHOICES:
        records = NotificationStats.objects.filter(receiver_type=receiver_type)
        count = records.count()
        bugs = sum(record.bug_count for record in records)
        print(f"  {display_name}: {count} 条记录, {bugs} 个Bug")
    
    # 按接收人统计
    print("\n按接收人统计:")
    receivers = NotificationStats.objects.values_list('receiver_name', flat=True).distinct()
    for receiver in receivers:
        records = NotificationStats.objects.filter(receiver_name=receiver)
        count = records.count()
        bugs = sum(record.bug_count for record in records)
        print(f"  {receiver}: {count} 条记录, {bugs} 个Bug")
    
    print("\n" + "=" * 60)


if __name__ == '__main__':
    print("推送提醒统计功能基础测试脚本")
    print("=" * 60)
    
    # 基础功能测试
    test_basic_stats()
    
    # 责任人推送统计测试
    test_assignee_notification()
    
    # TMDE推送统计测试
    test_tmde_notification()
    
    # 显示最终统计汇总
    show_final_summary()
    
    print("\n测试脚本执行完成！")
    print("\n可以通过以下API接口查询统计数据:")
    print("- 每日统计: GET /task_manager/notification-stats/daily/?date=2025-06-24")
    print("- 时间段统计: GET /task_manager/notification-stats/period/?start_date=2025-06-24&end_date=2025-06-24")
    print("- 统计列表: GET /task_manager/notification-stats/list/")
    print("- 统计汇总: GET /task_manager/notification-stats/summary/?days=7")
    print("\n数据库文件位置: db.sqlite3")
    print("可以使用SQLite工具查看 task_manager_notificationstats 表中的数据")
