#!/usr/bin/env python
"""
测试推送提醒统计功能的脚本
"""

import os
import sys
import django
from datetime import datetime, date

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'transsiongroot.settings')
django.setup()

from task_manager.backend.src.tools.jira_tools_v1 import JiraTools
from task_manager.backend.src.tools.notification_stats import NotificationStatsService
from task_manager.models import NotificationStats


def test_notification_stats():
    """
    测试推送提醒统计功能
    """
    print("开始测试推送提醒统计功能...")
    
    # 创建统计服务实例
    stats_service = NotificationStatsService()
    
    # 测试数据
    test_bug_data = [
        {
            'TOS1510-12345': {
                'url': 'http://jira.transsion.com/browse/TOS1510-12345',
                'created': '2025-06-24T10:00:00.000+0800',
                'updated': '2025-06-24T11:00:00.000+0800',
                'flag': True
            }
        },
        {
            'TOS1510-12346': {
                'url': 'http://jira.transsion.com/browse/TOS1510-12346',
                'created': '2025-06-24T10:30:00.000+0800',
                'updated': '2025-06-24T11:30:00.000+0800',
                'flag': False
            }
        }
    ]
    
    # 测试记录推送统计
    print("\n1. 测试记录推送统计...")
    success = stats_service.record_notification(
        notification_type='submitted',
        receiver_type='assignee',
        receiver_name='测试用户',
        receiver_username='test.user',
        bug_data=test_bug_data,
        push_content='{"text":"测试推送内容"}',
        push_success=True
    )
    print(f"记录推送统计结果: {success}")
    
    # 测试记录TMDE推送统计
    print("\n2. 测试记录TMDE推送统计...")
    success = stats_service.record_notification(
        notification_type='submitted',
        receiver_type='tmde',
        receiver_name='测试TMDE',
        receiver_username='test.tmde',
        tmde_name='测试TMDE',
        bug_data=test_bug_data,
        push_content='{"text":"测试TMDE推送内容"}',
        push_success=True
    )
    print(f"记录TMDE推送统计结果: {success}")
    
    # 测试获取今日统计
    print("\n3. 测试获取今日统计...")
    daily_stats = stats_service.get_daily_stats()
    print(f"今日统计数据: {daily_stats}")
    
    # 测试获取时间段统计
    print("\n4. 测试获取时间段统计...")
    today = date.today()
    period_stats = stats_service.get_period_stats(today, today)
    print(f"时间段统计数据: {period_stats}")
    
    # 查询数据库中的记录
    print("\n5. 查询数据库中的统计记录...")
    records = NotificationStats.objects.all().order_by('-created_at')[:5]
    for record in records:
        print(f"记录ID: {record.id}, 类型: {record.get_notification_type_display()}, "
              f"接收人: {record.receiver_name}, Bug数量: {record.bug_count}, "
              f"推送日期: {record.push_date}")
    
    print("\n测试完成！")


def test_jira_integration():
    """
    测试与JIRA工具的集成
    """
    print("\n开始测试与JIRA工具的集成...")
    
    # 注意：这里只是演示如何集成，实际运行需要有效的JIRA连接
    try:
        # 创建JiraTools实例（使用测试配置）
        jira_tools = JiraTools(
            jira_url="http://test.jira.com/",
            jira_user="test_user",
            jira_password="test_password"
        )
        
        # 模拟推送数据
        test_assignee_data = {
            'test.user': [
                {
                    'TOS1510-12345': {
                        'url': 'http://jira.transsion.com/browse/TOS1510-12345',
                        'created': '2025-06-24T10:00:00.000+0800',
                        'updated': '2025-06-24T11:00:00.000+0800',
                        'flag': True
                    }
                }
            ]
        }
        
        test_tmde_data = {
            'test.tmde': {
                'test.user': [
                    {
                        'TOS1510-12346': {
                            'url': 'http://jira.transsion.com/browse/TOS1510-12346',
                            'created': '2025-06-24T10:30:00.000+0800',
                            'updated': '2025-06-24T11:30:00.000+0800',
                            'flag': False
                        }
                    }
                ]
            }
        }
        
        # 测试责任人推送统计
        print("测试责任人推送统计...")
        record_ids = jira_tools.stats_service.record_assignee_notification(
            notification_type='submitted',
            assignee_data=test_assignee_data,
            prompt='测试待审核问题'
        )
        print(f"创建的责任人统计记录ID: {record_ids}")
        
        # 测试TMDE推送统计
        print("测试TMDE推送统计...")
        record_ids = jira_tools.stats_service.record_tmde_notification(
            notification_type='submitted',
            tmde_data=test_tmde_data,
            prompt='测试待审核问题'
        )
        print(f"创建的TMDE统计记录ID: {record_ids}")
        
        print("JIRA集成测试完成！")
        
    except Exception as e:
        print(f"JIRA集成测试出现错误（这是正常的，因为没有真实的JIRA连接）: {str(e)}")


def show_statistics_summary():
    """
    显示统计数据汇总
    """
    print("\n=== 推送提醒统计数据汇总 ===")
    
    # 总记录数
    total_records = NotificationStats.objects.count()
    print(f"总推送记录数: {total_records}")
    
    # 按推送类型统计
    print("\n按推送类型统计:")
    for notification_type, display_name in NotificationStats.NOTIFICATION_TYPE_CHOICES:
        count = NotificationStats.objects.filter(notification_type=notification_type).count()
        total_bugs = sum(
            record.bug_count for record in 
            NotificationStats.objects.filter(notification_type=notification_type)
        )
        print(f"  {display_name}: {count} 条记录, {total_bugs} 个Bug")
    
    # 按接收人类型统计
    print("\n按接收人类型统计:")
    for receiver_type, display_name in NotificationStats.RECEIVER_TYPE_CHOICES:
        count = NotificationStats.objects.filter(receiver_type=receiver_type).count()
        total_bugs = sum(
            record.bug_count for record in 
            NotificationStats.objects.filter(receiver_type=receiver_type)
        )
        print(f"  {display_name}: {count} 条记录, {total_bugs} 个Bug")
    
    # 今日统计
    today = date.today()
    today_records = NotificationStats.objects.filter(push_date=today)
    today_count = today_records.count()
    today_bugs = sum(record.bug_count for record in today_records)
    print(f"\n今日推送统计: {today_count} 条记录, {today_bugs} 个Bug")
    
    # 最近的推送记录
    print("\n最近5条推送记录:")
    recent_records = NotificationStats.objects.order_by('-created_at')[:5]
    for record in recent_records:
        print(f"  {record.created_at.strftime('%Y-%m-%d %H:%M:%S')} - "
              f"{record.get_notification_type_display()} - "
              f"{record.receiver_name} - "
              f"{record.bug_count}个Bug")


if __name__ == '__main__':
    print("推送提醒统计功能测试脚本")
    print("=" * 50)
    
    # 基础功能测试
    test_notification_stats()
    
    # JIRA集成测试
    test_jira_integration()
    
    # 显示统计汇总
    show_statistics_summary()
    
    print("\n" + "=" * 50)
    print("测试脚本执行完成！")
    print("\n可以通过以下API接口查询统计数据:")
    print("- 每日统计: GET /task_manager/notification-stats/daily/?date=2025-06-24")
    print("- 时间段统计: GET /task_manager/notification-stats/period/?start_date=2025-06-24&end_date=2025-06-24")
    print("- 统计列表: GET /task_manager/notification-stats/list/")
    print("- 统计汇总: GET /task_manager/notification-stats/summary/?days=7")
