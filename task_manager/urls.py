# task_manager/urls.py
from django.urls import path
from . import views
from .diy_views import jira_bugs,my_test


urlpatterns = [
    # 缺陷数据分析
    # Bug管理
    path('bugs_index/', jira_bugs.bugs_index, name='bugs_index'),  # 列表页
    path('bugs_list/', jira_bugs.bugs_list, name='bugs_list'),  # 获取所有bug
    path('bug_update/<str:issue_key>/', jira_bugs.bug_update, name='bug_update'),
    path('upload/', jira_bugs.upload_file_v1, name='upload_file'),# 上传文件

    path('bug_delete_batch/', jira_bugs.bug_delete_batch, name='bug_delete_batch'),#  批量删除
    path('export_bug_list/', jira_bugs.export_bug_list, name='export_bug_list'),# 导出bug列表
    path('save_parameters/', jira_bugs.save_parameters, name='save_parameters'),# 导出bug列表

    # 推送提醒统计相关接口
    path('notification-stats/daily/', views.notification_stats_daily, name='notification_stats_daily'),
    path('notification-stats/period/', views.notification_stats_period, name='notification_stats_period'),
    path('notification-stats/list/', views.notification_stats_list, name='notification_stats_list'),
    path('notification-stats/summary/', views.notification_stats_summary, name='notification_stats_summary'),

    path('bugs_index_test/', my_test.bugs_index_test, name='bugs_index'),  # 列表页

]
