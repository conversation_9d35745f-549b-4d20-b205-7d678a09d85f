from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from datetime import datetime, date, timedelta
import json

from .models import NotificationStats
from .backend.src.tools.notification_stats import NotificationStatsService

# Create your views here.

@csrf_exempt
def notification_stats_daily(request):
    """
    获取指定日期的推送统计数据
    """
    if request.method == 'GET':
        # 获取日期参数，默认为今天
        date_str = request.GET.get('date', timezone.now().date().isoformat())
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'error': '日期格式错误，请使用YYYY-MM-DD格式'}, status=400)

        stats_service = NotificationStatsService()
        stats = stats_service.get_daily_stats(target_date)

        return JsonResponse({
            'success': True,
            'data': stats
        })

    return JsonResponse({'error': '不支持的请求方法'}, status=405)


@csrf_exempt
def notification_stats_period(request):
    """
    获取指定时间段的推送统计数据
    """
    if request.method == 'GET':
        # 获取时间段参数
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if not start_date_str or not end_date_str:
            return JsonResponse({'error': '请提供start_date和end_date参数'}, status=400)

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'error': '日期格式错误，请使用YYYY-MM-DD格式'}, status=400)

        if start_date > end_date:
            return JsonResponse({'error': '开始日期不能大于结束日期'}, status=400)

        stats_service = NotificationStatsService()
        stats = stats_service.get_period_stats(start_date, end_date)

        return JsonResponse({
            'success': True,
            'data': stats
        })

    return JsonResponse({'error': '不支持的请求方法'}, status=405)


@csrf_exempt
def notification_stats_list(request):
    """
    获取推送统计数据列表
    """
    if request.method == 'GET':
        # 获取查询参数
        notification_type = request.GET.get('notification_type')
        receiver_type = request.GET.get('receiver_type')
        receiver_name = request.GET.get('receiver_name')
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 构建查询条件
        queryset = NotificationStats.objects.all()

        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)

        if receiver_type:
            queryset = queryset.filter(receiver_type=receiver_type)

        if receiver_name:
            queryset = queryset.filter(receiver_name__icontains=receiver_name)

        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(push_date__gte=start_date)
            except ValueError:
                return JsonResponse({'error': '开始日期格式错误'}, status=400)

        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(push_date__lte=end_date)
            except ValueError:
                return JsonResponse({'error': '结束日期格式错误'}, status=400)

        # 分页
        total_count = queryset.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        records = queryset[start_index:end_index]

        # 序列化数据
        data = []
        for record in records:
            data.append({
                'id': record.id,
                'notification_type': record.notification_type,
                'notification_type_display': record.get_notification_type_display(),
                'receiver_type': record.receiver_type,
                'receiver_type_display': record.get_receiver_type_display(),
                'receiver_name': record.receiver_name,
                'receiver_username': record.receiver_username,
                'tmde_name': record.tmde_name,
                'bug_count': record.bug_count,
                'bug_details': record.bug_details,
                'push_success': record.push_success,
                'push_error_msg': record.push_error_msg,
                'created_at': record.created_at.isoformat(),
                'push_date': record.push_date.isoformat(),
            })

        return JsonResponse({
            'success': True,
            'data': {
                'records': data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })

    return JsonResponse({'error': '不支持的请求方法'}, status=405)


@csrf_exempt
def notification_stats_summary(request):
    """
    获取推送统计汇总数据
    """
    if request.method == 'GET':
        # 获取时间范围参数，默认为最近7天
        days = int(request.GET.get('days', 7))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days-1)

        queryset = NotificationStats.objects.filter(
            push_date__gte=start_date,
            push_date__lte=end_date
        )

        # 总体统计
        total_notifications = queryset.count()
        total_bugs = sum(record.bug_count for record in queryset)

        # 按推送类型统计
        type_stats = {}
        for notification_type, display_name in NotificationStats.NOTIFICATION_TYPE_CHOICES:
            type_queryset = queryset.filter(notification_type=notification_type)
            type_stats[notification_type] = {
                'display_name': display_name,
                'count': type_queryset.count(),
                'bug_count': sum(record.bug_count for record in type_queryset)
            }

        # 按接收人类型统计
        receiver_type_stats = {}
        for receiver_type, display_name in NotificationStats.RECEIVER_TYPE_CHOICES:
            type_queryset = queryset.filter(receiver_type=receiver_type)
            receiver_type_stats[receiver_type] = {
                'display_name': display_name,
                'count': type_queryset.count(),
                'bug_count': sum(record.bug_count for record in type_queryset)
            }

        # 按日期统计
        daily_stats = {}
        current_date = start_date
        while current_date <= end_date:
            daily_queryset = queryset.filter(push_date=current_date)
            daily_stats[current_date.isoformat()] = {
                'count': daily_queryset.count(),
                'bug_count': sum(record.bug_count for record in daily_queryset)
            }
            current_date += timedelta(days=1)

        return JsonResponse({
            'success': True,
            'data': {
                'period': f"{start_date.isoformat()} to {end_date.isoformat()}",
                'total_notifications': total_notifications,
                'total_bugs': total_bugs,
                'by_notification_type': type_stats,
                'by_receiver_type': receiver_type_stats,
                'daily_stats': daily_stats
            }
        })

    return JsonResponse({'error': '不支持的请求方法'}, status=405)
