<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推送提醒统计数据</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stats-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stats-card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stat-label {
            font-weight: bold;
            color: #555;
        }
        .stat-value {
            color: #2980b9;
            font-weight: bold;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .controls label {
            margin-right: 10px;
            font-weight: bold;
        }
        .controls input, .controls select {
            margin-right: 15px;
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .controls button:hover {
            background-color: #2980b9;
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        .error {
            color: #e74c3c;
            background-color: #fadbd8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #27ae60;
            background-color: #d5f4e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推送提醒统计数据</h1>
        
        <div class="controls">
            <label for="days">统计天数:</label>
            <select id="days">
                <option value="1">今天</option>
                <option value="7" selected>最近7天</option>
                <option value="30">最近30天</option>
            </select>
            
            <label for="startDate">开始日期:</label>
            <input type="date" id="startDate">
            
            <label for="endDate">结束日期:</label>
            <input type="date" id="endDate">
            
            <button onclick="loadSummary()">加载汇总</button>
            <button onclick="loadPeriodStats()">加载时间段统计</button>
            <button onclick="loadDetailList()">加载详细列表</button>
        </div>

        <div id="summaryStats" class="stats-grid">
            <!-- 汇总统计数据将在这里显示 -->
        </div>

        <div id="detailStats">
            <!-- 详细统计数据将在这里显示 -->
        </div>

        <div class="table-container">
            <div id="recordsList">
                <!-- 记录列表将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = sevenDaysAgo.toISOString().split('T')[0];
            
            // 自动加载汇总数据
            loadSummary();
        });

        async function loadSummary() {
            const days = document.getElementById('days').value;
            const summaryDiv = document.getElementById('summaryStats');
            
            summaryDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`/task_manager/notification-stats/summary/?days=${days}`);
                const result = await response.json();
                
                if (result.success) {
                    displaySummary(result.data);
                } else {
                    summaryDiv.innerHTML = '<div class="error">加载失败</div>';
                }
            } catch (error) {
                summaryDiv.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        }

        function displaySummary(data) {
            const summaryDiv = document.getElementById('summaryStats');
            
            let html = `
                <div class="stats-card">
                    <h3>总体统计</h3>
                    <div class="stat-item">
                        <span class="stat-label">统计时间段:</span>
                        <span class="stat-value">${data.period}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总推送次数:</span>
                        <span class="stat-value">${data.total_notifications}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总Bug数量:</span>
                        <span class="stat-value">${data.total_bugs}</span>
                    </div>
                </div>
                
                <div class="stats-card">
                    <h3>按推送类型统计</h3>
            `;
            
            for (const [type, stats] of Object.entries(data.by_notification_type)) {
                html += `
                    <div class="stat-item">
                        <span class="stat-label">${stats.display_name}:</span>
                        <span class="stat-value">${stats.count}次 (${stats.bug_count}个Bug)</span>
                    </div>
                `;
            }
            
            html += `</div>
                <div class="stats-card">
                    <h3>按接收人类型统计</h3>
            `;
            
            for (const [type, stats] of Object.entries(data.by_receiver_type)) {
                html += `
                    <div class="stat-item">
                        <span class="stat-label">${stats.display_name}:</span>
                        <span class="stat-value">${stats.count}次 (${stats.bug_count}个Bug)</span>
                    </div>
                `;
            }
            
            html += '</div>';
            
            summaryDiv.innerHTML = html;
        }

        async function loadPeriodStats() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const detailDiv = document.getElementById('detailStats');
            
            if (!startDate || !endDate) {
                detailDiv.innerHTML = '<div class="error">请选择开始和结束日期</div>';
                return;
            }
            
            detailDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`/task_manager/notification-stats/period/?start_date=${startDate}&end_date=${endDate}`);
                const result = await response.json();
                
                if (result.success) {
                    displayPeriodStats(result.data);
                } else {
                    detailDiv.innerHTML = '<div class="error">加载失败</div>';
                }
            } catch (error) {
                detailDiv.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        }

        function displayPeriodStats(data) {
            const detailDiv = document.getElementById('detailStats');
            
            let html = `
                <div class="stats-card">
                    <h3>时间段统计 (${data.period})</h3>
                    <div class="stat-item">
                        <span class="stat-label">总推送次数:</span>
                        <span class="stat-value">${data.total_notifications}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总Bug数量:</span>
                        <span class="stat-value">${data.total_bugs}</span>
                    </div>
                    <h4>每日明细:</h4>
            `;
            
            for (const [date, stats] of Object.entries(data.daily_breakdown)) {
                html += `
                    <div class="stat-item">
                        <span class="stat-label">${date}:</span>
                        <span class="stat-value">${stats.total_notifications}次推送, ${stats.total_bugs}个Bug</span>
                    </div>
                `;
            }
            
            html += '</div>';
            detailDiv.innerHTML = html;
        }

        async function loadDetailList() {
            const recordsDiv = document.getElementById('recordsList');
            recordsDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch('/task_manager/notification-stats/list/?page_size=50');
                const result = await response.json();
                
                if (result.success) {
                    displayDetailList(result.data);
                } else {
                    recordsDiv.innerHTML = '<div class="error">加载失败</div>';
                }
            } catch (error) {
                recordsDiv.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        }

        function displayDetailList(data) {
            const recordsDiv = document.getElementById('recordsList');
            
            let html = `
                <h3>详细记录列表 (共${data.total_count}条记录)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>推送类型</th>
                            <th>接收人类型</th>
                            <th>接收人</th>
                            <th>Bug数量</th>
                            <th>推送状态</th>
                            <th>推送日期</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            for (const record of data.records) {
                html += `
                    <tr>
                        <td>${record.id}</td>
                        <td>${record.notification_type_display}</td>
                        <td>${record.receiver_type_display}</td>
                        <td>${record.receiver_name}</td>
                        <td>${record.bug_count}</td>
                        <td>${record.push_success ? '成功' : '失败'}</td>
                        <td>${record.push_date}</td>
                        <td>${new Date(record.created_at).toLocaleString()}</td>
                    </tr>
                `;
            }
            
            html += `
                    </tbody>
                </table>
                <p>显示第${data.page}页，共${data.total_pages}页</p>
            `;
            
            recordsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
