import matplotlib.pyplot as plt

# 合并后的数据（已处理大小写不一致项，小问题应用合并为"其他"）
applications = [
    "Weather", "TransID", "Transsion Tips", "Notes", "OSService",
    "Calendar", "Dialer", "OScloud", "其他"
]
problems = [384, 30, 23, 20, 18, 15, 12, 11, 23]  # OScloud = 2(OSCloud) + 9(OScloud)
# "其他"包含: Contact(1), OOBE(1), SMS(1), <PERSON>(3), Theme(3), SmartMessage(6), Clock(8)

# 创建饼状图
plt.figure(figsize=(12, 8))
wedges, texts, autotexts = plt.pie(
    problems,
    autopct='%1.1f%%',
    startangle=140,
    colors=plt.cm.tab20.colors,
    pctdistance=0.85
)

# 添加图例
plt.legend(
    wedges,
    [f"{app} ({prob})" for app, prob in zip(applications, problems)],
    title="应用分布",
    loc="center left",
    bbox_to_anchor=(1, 0, 0.5, 1)
)

# 添加标题
plt.title("应用问题数量分布", fontsize=16, pad=20)
plt.axis('equal')  # 确保饼图为正圆

plt.tight_layout()
plt.show()