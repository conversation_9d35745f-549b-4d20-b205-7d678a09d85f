import json
import os
import time
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict

from jira import JIRA

from task_manager.backend.src.tools.feishu_tools import FeishuBot
from task_manager.backend.src.tools.notification_stats import NotificationStatsService

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


class JiraTools:
    def __init__(self, jira_url='http://jira.transsion.com', jira_user='changyi.bu', jira_password='Zdd&88329'):
        self.jira_url = jira_url
        self.jira_user = jira_user
        self.jira_password = jira_password
        self.jira = JIRA(
            server=self.jira_url,
            basic_auth=(self.jira_user, self.jira_password),
            options={"verify": False},
        )

        self.default_host = {
            'zh': "http://jira.transsion.com",
            'ex': 'http://jira-ex.transsion.com:6001'
        }

        self.feishu_bot = FeishuBot()
        self.stats_service = NotificationStatsService()

        self.all_users = self.get_all_users()

        self.component_owner = {
            'Questionnaire': {'owner': '王冬来', 'TMDE': '陈程'},
            'Contact': {'owner': '仇玉婷', 'TMDE': '陈程'},
            'Dialer': {'owner': '屠新田', 'TMDE': '陈程'},
            'SmartMessage': {'owner': '冯思远', 'TMDE': '陈程'},
            'Messages': {'owner': '冯思远', 'TMDE': '陈程'},
            'Google APPS_tOS': {'owner': '仇玉婷', 'TMDE': '陈程'},
            'GoogleContact': {'owner': '仇玉婷', 'TMDE': '陈程'},
            'GooglePhone': {'owner': '仇玉婷', 'TMDE': '陈程'},
            'GoogleMessage': {'owner': '仇玉婷', 'TMDE': '陈程'},
            'OScloud': {'owner': '王冬来', 'TMDE': '陈程'},
            'Ewarrantycard': {'owner': '宓玄玄', 'TMDE': '陈程'},
            'TransFind': {'owner': '田弟川', 'TMDE': '陈程'},
            'InfinixID（TecnoIDAndItelID）': {'owner': '田弟川', 'TMDE': '陈程'},
            'Feedback': {'owner': '王冬来', 'TMDE': '陈程'},
            'TPMS': {'owner': '王冬来', 'TMDE': '陈程'},
            'Finder': {'owner': '田弟川', 'TMDE': '陈程'},
            'Energy storage': {'owner': '王冬来', 'TMDE': '陈程'},
            'SmartTouch': {'owner': '卜昌义', 'TMDE': '陈程'},
            'TranssionTips': {'owner': '陶书红', 'TMDE': '陈程'},
            'Recorder': {'owner': '陶书红', 'TMDE': '陈程'},
            'Phone Master': {'owner': '王冬来', 'TMDE': '陈程'},
            'Ella': {'owner': '冯思远', 'TMDE': '陈程'},
            'MOL': {'owner': '卜昌义', 'TMDE': '陈程'},
            'AICoreModelManager': {'owner': '卜昌义', 'TMDE': '陈程'},
            'Ella识屏': {'owner': '卜昌义', 'TMDE': '陈程'},
            'DemoMode': {'owner': '陶书红', 'TMDE': '陈程'},
            'Calculator': {'owner': '金倩倩', 'TMDE': '包晓琴'},
            'AIGallery': {'owner': '王新星', 'TMDE': '包晓琴'},
            'Clipper': {'owner': '朱柯柯', 'TMDE': '包晓琴'},
            'Vishaplayer': {'owner': '孙友', 'TMDE': '包晓琴'},
            'Flipmusic': {'owner': '吕晓燕', 'TMDE': '包晓琴'},
            'Notes': {'owner': '郭霖', 'TMDE': '包晓琴'},
            'Smart Switch': {'owner': '金倩倩', 'TMDE': '包晓琴'},
            'FileManager': {'owner': '王涛', 'TMDE': '包晓琴'},
            'Weather': {'owner': '朱柯柯', 'TMDE': '包晓琴'},
            'Clock': {'owner': '吕晓燕', 'TMDE': '包晓琴'},
            'Calendar': {'owner': '朱柯柯', 'TMDE': '包晓琴'},
            'FMRadio': {'owner': '王涛', 'TMDE': '包晓琴'},
            'SmartScanner': {'owner': '王涛', 'TMDE': '包晓琴'},

        }

    def init(self, jira_url):
        jira = JIRA(
            server=jira_url,
            basic_auth=(self.jira_user, self.jira_password),
            options={"verify": False},
        )
        return jira

    def get_url_source(self, filter_url):
        if 'http://jira-ex.transsion.com' in filter_url:
            source = 'ex'
        elif 'http://jira.transsion.com' in filter_url:
            source = 'zh'
        else:
            source = 'zh'
        return source

    def get_issues(self, jal):
        issues = self.jira.search_issues(jal)
        return [issue.key for issue in issues]

    # 1、通过filter获取bug列表
    def get_bugs_by_filter(self, filter_url):
        """
        Get bugs by filter
        """
        # 判断是国内还是海外库
        source = self.get_url_source(filter_url)
        if source == 'ex':
            self.jira = self.init(self.default_host.get(source))
        host = self.default_host.get(source)
        # 获取缺陷列表
        filter_id = filter_url.split('=')[-1]
        jql = f'filter={filter_id}'
        issues = self.jira.search_issues(jql, maxResults=False)  # maxResults=False表示获取所有结果
        bug_list = {}
        for issue in issues:
            # print(f'Key: {issue.key}, Summary: {issue.fields.summary}')
            # print(f'Key: {issue.key}, assignee: {issue.fields.assignee.displayName}')
            # print(f'Key: {issue.key}, assignee: {issue.fields.assignee.key}')
            # print(f'Key: {issue.key}, assignee: {issue.fields.assignee.name}') # 名称
            # print(f'Key: {issue.key}, component: {issue.fields.components.name}')
            # print(f'Key: {issue.key}, status: {issue.fields.status}')
            url = f'{host}/browse/{issue.key}'
            if bug_list.get(issue.fields.assignee.name, None):
                bug_list[issue.fields.assignee.name].append(url)
                # bug_list[issue.fields.assignee.displayName].append(url)
            else:
                bug_list.update({
                    issue.fields.assignee.name: [url]
                })
        return bug_list

    def get_bugs_by_jql(self, jql_str, source='zh'):

        if source == 'ex':
            self.jira = self.init(self.default_host.get(source))
        host = self.default_host.get(source)
        issues = self.jira.search_issues(jql_str, maxResults=False)
        bug_list = {}
        for issue in issues:
            url = f'{host}/browse/{issue.key}'
            flag = self.is_over_24_hours(issue.fields.created, issue.fields.updated)
            if bug_list.get(issue.fields.assignee.name, None):
                bug_list[issue.fields.assignee.name].append(
                    {issue.key: {'url': url, 'created': issue.fields.created, 'updated': issue.fields.updated,
                                 'flag': flag}
                     })
            else:  # '2025-06-09T21:47:30.000+0800'  '2025-06-09T21:45:41.000+0800'
                bug_list.update({
                    issue.fields.assignee.name: [{issue.key: {'url': url, 'created': issue.fields.created,
                                                              'updated': issue.fields.updated, 'flag': flag}}]
                })
        return bug_list

    def get_bugs_by_jql_component(self, jql_str, source='zh'):

        if source == 'ex':
            self.jira = self.init(self.default_host.get(source))
        host = self.default_host.get(source)
        issues = self.jira.search_issues(jql_str, maxResults=False)
        bug_list = {}
        for issue in issues:
            url = f'{host}/browse/{issue.key}'
            # 判断是否超过时限
            if issue.fields.status == 'Submitted':
                time_zone = 24
            elif issue.fields.status in ['Open', 'In Progress', 'Reopened', 'Resolved', 'Submitted',
                                         'Verified'] and issue.fields.resolution in ["Platform Limit", "Won't Fix",
                                                                                     'Reject']:
                time_zone = 48
            else:
                time_zone = 24
            flag = self.is_over_24_hours(issue.fields.created, issue.fields.updated, time_zone=time_zone)
            # 处理有多个模块的，取第一个模块
            components_name = issue.fields.components[0].name

            if bug_list.get(components_name, None):
                bug_list[components_name].append(
                    {issue.key: {'url': url, 'created': issue.fields.created, 'updated': issue.fields.updated,
                                 'flag': flag}
                     })
            else:  # '2025-06-09T21:47:30.000+0800'  '2025-06-09T21:45:41.000+0800'
                bug_list.update({
                    components_name: [{issue.key: {'url': url, 'created': issue.fields.created,
                                                   'updated': issue.fields.updated, 'flag': flag}}]
                })
        return bug_list

    def get_bugs_by_jql_component_tmde(self, jql_str, source='zh'):

        if source == 'ex':
            self.jira = self.init(self.default_host.get(source))
        host = self.default_host.get(source)
        issues = self.jira.search_issues(jql_str, maxResults=False)
        bug_list = {}
        bugs_tmde_list = {}
        for issue in issues:
            url = f'{host}/browse/{issue.key}'
            # 判断是否超过时限
            submitted_flag = False

            if issue.fields.status == 'Submitted':
                time_zone = 24
                submitted_flag = True
            elif  issue.fields.resolution in ["Platform Limit", "Won't Fix",'Reject']:
                time_zone = 48

            elif issue.fields.status == 'Resolved' and issue.fields.resolution not in ["Platform Limit", "Won't Fix",
                                                                                     'Reject']:
                time_zone = 72
            else:
                time_zone = 24

            flag = self.is_over_24_hours(issue.fields.created, issue.fields.updated, time_zone=time_zone,
                                         submitted_flag=submitted_flag)
            # 处理有多个模块的，取第一个模块
            components_name = issue.fields.components[0].name

            if bug_list.get(components_name, None):
                bug_list[components_name].append(
                    {issue.key: {'url': url, 'created': issue.fields.created, 'updated': issue.fields.updated,
                                 'flag': flag}
                     })
            else:  # '2025-06-09T21:47:30.000+0800'  '2025-06-09T21:45:41.000+0800'
                bug_list.update({
                    components_name: [{issue.key: {'url': url, 'created': issue.fields.created,
                                                   'updated': issue.fields.updated, 'flag': flag}}]
                })

            if flag:
                if bugs_tmde_list.get(components_name, None):
                    bugs_tmde_list[components_name].append(
                        {issue.key: {'url': url, 'created': issue.fields.created, 'updated': issue.fields.updated,
                                     'flag': flag}
                         })
                else:  # '2025-06-09T21:47:30.000+0800'  '2025-06-09T21:45:41.000+0800'
                    bugs_tmde_list.update({
                        components_name: [{issue.key: {'url': url, 'created': issue.fields.created,
                                                       'updated': issue.fields.updated, 'flag': flag}}]
                    })

        result = {
            'components_bugs': bug_list,
            'components_bugs_tmde': bugs_tmde_list
        }
        # print(json.dumps(result, ensure_ascii=False))
        return result

    def get_bugs_by_jql_assignee_tmde(self, jql_str, source='zh'):

        if source == 'ex':
            self.jira = self.init(self.default_host.get(source))
        host = self.default_host.get(source)
        issues = self.jira.search_issues(jql_str, maxResults=False)
        bug_list = {}
        bugs_tmde_list = {}
        for issue in issues:
            url = f'{host}/browse/{issue.key}'

            # 判断是否超过时限
            submitted_flag = False
            if issue.fields.status == 'Submitted':
                time_zone = 24
                submitted_flag = True
            elif issue.fields.resolution in ["Platform Limit", "Won't Fix",'Reject']:
                time_zone = 48
            elif issue.fields.status == 'Resolved' and issue.fields.resolution not in ["Platform Limit", "Won't Fix",'Reject']:
                time_zone = 72
            else:
                time_zone = 24
            # print(issue.key, time_zone, submitted_flag, issue.fields.status, issue.fields.updated,issue.fields.resolution)
            flag = self.is_over_24_hours(issue.fields.created, issue.fields.updated, time_zone=time_zone,
                                         submitted_flag=submitted_flag)
            # print(flag)
            # 按照assignee分组
            assignee = issue.fields.assignee.name
            created = issue.fields.created
            updated = issue.fields.updated

            if bug_list.get(assignee, None):
                bug_list[assignee].append(
                    {issue.key: {'url': url, 'created': created, 'updated': updated, 'flag': flag}
                     })
            else:  # '2025-06-09T21:47:30.000+0800'  '2025-06-09T21:45:41.000+0800'
                bug_list.update({
                    assignee: [{issue.key: {'url': url, 'created': created, 'updated': updated, 'flag': flag}}]
                })

            if flag:
                # {'陈程'：['卜昌义'：[],'屠新田'：[],'包晓琴'：['卜昌义'：[],'屠新田'：[]}
                team = {
                    'xintian.tu': 'cheng.chen',
                    'siyuan.feng': 'cheng.chen',
                    'yuting.qiu': 'cheng.chen',
                    'shuhong.tao': 'cheng.chen',
                    'xuanxuan.mi': 'cheng.chen',
                    'changyi.bu': 'cheng.chen',
                    'dichuan.tian': 'cheng.chen',
                    'donglai.wang': 'cheng.chen',
                }
                TMDE = team.get(assignee, 'xiaoqin.bao')  # 获取TMDE，获取不到默认给到xiaoqin.bao
                if bugs_tmde_list.get(TMDE, None):
                    if bugs_tmde_list.get(TMDE).get(assignee, None):
                        bugs_tmde_list[TMDE][assignee].append(
                            {issue.key: {'url': url, 'created': created, 'updated': updated, 'flag': flag}})
                    else:
                        bugs_tmde_list[TMDE].update({
                            assignee: [{issue.key: {'url': url, 'created': updated, 'updated': updated, 'flag': flag}}]
                        })
                else:
                    bugs_tmde_list.update({
                        TMDE: {
                            assignee: [{issue.key: {'url': url, 'created': created, 'updated': updated, 'flag': flag}}]}
                    })

        result = {
            'assignee_bugs': bug_list,
            'tmde_bugs': bugs_tmde_list
        }
        return result

    # 2、通过bug列表获取bug详情
    def batch_send_bug_notification_v2(self, filter_url_list: list):
        """
        Send bug notification to Feishu

        """
        bugs_list = {}
        for filter_url in filter_url_list:
            try:
                bugs = self.get_bugs_by_filter(filter_url)
                for assignee, bug_data in bugs.items():
                    if assignee in bugs_list.keys():  # 存在，者将bug添加到owner的列表中
                        [bugs_list[assignee].append(bug) for bug in bug_data]
                    else:  # 不存在，则创建
                        bugs_list[assignee] = bug_data
            except Exception as e:
                print(traceback.format_exc())
        print(bugs_list)
        for assignee, bug_data in bugs_list.items():
            print(bug_data)
            # assignee = re.findall(r'\((.*?)\)', assignee)[0]
            assignee = assignee
            print(assignee)
            tmp = '{"text":"待切单列表,请关注：'
            for i in bug_data:
                tmp += i + ' ' + '\\n' + ' '

            tmp = tmp + '"}'
            print(tmp)
            self.feishu_bot.send_bug_notification_v6(assignee, tmp)
            time.sleep(1)

    # 3、根据bug信息中的assignee发送飞书通知

    def do_batch_send_bug_notification_v2(self):
        filter_list = [
            'http://jira.transsion.com/issues/?filter=108312',  # 独立产品Submitted状态待激活问题
            # 'http://jira.transsion.com/issues/?filter=108538',
            # 'http://jira.transsion.com/issues/?filter=108539',
            # 'http://jira.transsion.com/issues/?filter=108316',
            # 'http://jira.transsion.com/issues/?filter=108317',
            "http://jira-ex.transsion.com:6001/issues/?filter=29701",  # 【待激活】独立产品海外库所有Submitted问题
        ]
        self.batch_send_bug_notification_v2(filter_list)

    # 计算两个时间戳之间时间是否超过24小时
    def is_over_24_hours(self, created, updated, time_zone=24, submitted_flag=False):
        """
        :param created:
        :param updated:
        :param time_zone: 默认24h
        :return:
        """
        # 解析时间字符串
        created_timestamp = datetime.strptime(created, "%Y-%m-%dT%H:%M:%S.%f%z")
        updated_timestamp = datetime.strptime(updated, "%Y-%m-%dT%H:%M:%S.%f%z")
        # 获取秒级时间戳
        if submitted_flag:  # 是否为待审核,
            # 待审核缺陷，需要按照【当前时间-创建时间】来判断是否超过24小时
            begin = created_timestamp.timestamp()
            end = time.time()  # 当前时间判断
        else:
            # 非待审核缺陷，需要按照【当前时间-更新时间】来判断是否超过24小时
            begin = updated_timestamp.timestamp()
            end = time.time()

        if (end - begin) >= (int(time_zone)) * 60 * 60:
            return True
        return False

    def blocker_bug_notification(self):
        """
        submit 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': '(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, "Task Tracking", Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, "Google App 7.0", GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL, XLWYYEHLYE) AND issuetype = Bug AND status in ( Submitted) AND component in (" Vishaplayer", "AI Gallery", AICoreModelManager, AIGallery, AIGalleryOS, Calculator, Clipper, Contact, Contacts, DemoMode, Dialer, "Dialer ", Ella, Ella识屏, Ewarrantycard, Feedback, Finder, FlipMusic, "Google Contact", "Google Dialer", "Google Message", "Google Message go", GooglePhone, InCallUI, MOL, OScloud, PhoneMaster, Questionnaire, SmartMessage, SmartMessage_TN, SmartMessagePro, SmartTouch, SoundRecorder, TPMS, TransID, TranssionTips, Ulife, Vishaplayer, Vishaplayer（AI字幕）, 手机找回, OSService, AICoreService, Questionnaire, FM, Notes, OScloud, AICoreLLM) AND priority = Blocker',
                'source': 'zh',
            },
            {
                'jql_str': 'issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai, changyi.bu) AND priority = Blocker',
                'source': 'ex',
            }
        ]
        bugs_list = {}
        for item in jql_str_list:
            jql_str = item.get('jql_str')
            source = item.get('source')
            try:
                bugs = self.get_bugs_by_jql(jql_str, source)
                for assignee, bug_data in bugs.items():
                    if assignee in bugs_list.keys():  # 存在，者将bug添加到owner的列表中
                        [bugs_list[assignee].append(bug) for bug in bug_data]
                    else:  # 不存在，则创建
                        bugs_list[assignee] = bug_data
            except Exception as e:
                print(traceback.format_exc())
        # print(bugs_list)
        for assignee, bug_data in bugs_list.items():
            assignee = assignee
            # print(assignee)
            tmp = '{"text":"A类问题，请及时处理：'
            for i in bug_data:
                # print(i)
                for key, value in i.items():
                    tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)
            if assignee in ['王冬来', '仇玉婷', '屠新田', '冯思远', '宓玄玄', '田弟川', '陶书红', '卜昌义', '陈程']:
                self.feishu_bot.send_bug_notification_v6(assignee, tmp)
            # self.feishu_bot.send_bug_notification_v6(assignee, tmp)
            time.sleep(1)

    def resolved_bug_notification(self):
        """
        resolved 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': '(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, "Task Tracking", Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, "Google App 7.0", GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL, XLWYYEHLYE) AND issuetype = Bug AND status in ( Submitted) AND component in (" Vishaplayer", "AI Gallery", AICoreModelManager, AIGallery, AIGalleryOS, Calculator, Clipper, Contact, Contacts, DemoMode, Dialer, "Dialer ", Ella, Ella识屏, Ewarrantycard, Feedback, Finder, FlipMusic, "Google Contact", "Google Dialer", "Google Message", "Google Message go", GooglePhone, InCallUI, MOL, OScloud, PhoneMaster, Questionnaire, SmartMessage, SmartMessage_TN, SmartMessagePro, SmartTouch, SoundRecorder, TPMS, TransID, TranssionTips, Ulife, Vishaplayer, Vishaplayer（AI字幕）, 手机找回, OSService, AICoreService, Questionnaire, FM, Notes, OScloud, AICoreLLM) AND priority in (Critical,Major)',
                'source': 'zh',
            },
            {
                'jql_str': 'issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5,xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai, changyi.bu) AND priority in (Critical,Major)',
                'source': 'ex',
            }
        ]
        bugs_list = {}
        for item in jql_str_list:
            jql_str = item.get('jql_str')
            source = item.get('source')
            try:
                bugs = self.get_bugs_by_jql(jql_str, source)
                for assignee, bug_data in bugs.items():
                    if assignee in bugs_list.keys():  # 存在，者将bug添加到owner的列表中
                        [bugs_list[assignee].append(bug) for bug in bug_data]
                    else:  # 不存在，则创建
                        bugs_list[assignee] = bug_data
            except Exception as e:
                print(traceback.format_exc())
        # print(bugs_list)
        for assignee, bug_data in bugs_list.items():
            assignee = assignee
            # print(assignee)
            tmp = '{"text":"A类问题，请及时处理：'
            for i in bug_data:
                # print(i)
                for key, value in i.items():
                    tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)
            self.feishu_bot.send_bug_notification_v6(assignee, tmp)
            time.sleep(1)

    def get_all_users(self):
        json_file = os.path.join(DATA_JSON_DIR, 'user_id_2.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            all_users = ','.join(data.keys())
            # print(all_users)
            return all_users

    def format_bug_data(self, jql_str_list):
        bugs_list = {}
        bugs_tmde_list = {}

        for i in jql_str_list:
            jql_str = i.get('jql_str')
            source = i.get('source')
            try:
                bugs = self.get_bugs_by_jql_component_tmde(jql_str, source)
                for component, bug_data in bugs['components_bugs'].items():
                    if component in bugs_list.keys():  # 存在，者将bug添加到owner的列表中
                        [bugs_list[component].append(bug) for bug in bug_data]
                    else:  # 不存在，则创建
                        bugs_list[component] = bug_data
                for component, bug_data in bugs['components_bugs_tmde'].items():
                    if component in bugs_tmde_list.keys():  # 存在，者将bug添加到owner的列表中
                        [bugs_list[component].append(bug) for bug in bug_data]
                    else:  # 不存在，则创建
                        bugs_tmde_list[component] = bug_data
            except Exception as e:
                print(traceback.format_exc())
        result = {'assignee': bugs_list, 'tmde': bugs_tmde_list}
        print(json.dumps(result, ensure_ascii=False))
        return result

    def format_bug_data_by_assignee(self, jql_str_list):
        assignee_bugs = {}
        tmde_bugs = {}

        for i in jql_str_list:
            jql_str = i.get('jql_str')
            source = i.get('source')
            try:
                bugs = self.get_bugs_by_jql_assignee_tmde(jql_str, source)

                if bugs.get('assignee_bugs', None):  # 判断是否有值
                    for assignee, bug_data in bugs['assignee_bugs'].items():  # 循环遍历assignee
                        if assignee in assignee_bugs.keys():  # assignee存在，将bug添加到assignee的列表中
                            [assignee_bugs[assignee].append(bug) for bug in bug_data]
                        else:  # 不存在，则创建
                            assignee_bugs[assignee] = bug_data

                if bugs.get('tmde_bugs', None):  # 判断是否有值
                    for TMDE, bug_data in bugs['tmde_bugs'].items():  # 循环遍历TMDE
                        if TMDE in tmde_bugs.keys():  # TMDE_bug存在
                            for assignee, bug_data in bug_data.items():
                                if assignee in tmde_bugs[TMDE].keys():  # assignee存在，将bug添加到assignee的列表中
                                    [tmde_bugs[TMDE][assignee].append(bug) for bug in bug_data]
                                else:  # 不存在，则创建
                                    tmde_bugs[TMDE][assignee] = bug_data
                        else:  # 不存在，则创建
                            tmde_bugs[TMDE] = bug_data
            except Exception as e:
                print(traceback.format_exc())
        result = {'assignee_bugs': assignee_bugs, 'tmde_bugs': tmde_bugs}
        return result

    def blocker_bug_filter(self):
        """
        Won't fix\Reject\平台限制问题处理 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': f'issuetype = Bug AND status = Submitted AND assignee in ({self.all_users}) AND priority in (Blocker)AND createdDate >= startOfYear()',
                'source': 'zh',
            },
            {
                'jql_str': f'issuetype = Bug AND status = Submitted AND assignee in ({self.all_users}) AND priority in (Blocker)AND createdDate >= startOfYear()',
                'source': 'ex',
            }
        ]
        result = self.format_bug_data(jql_str_list)
        return result

    def submit_bug_filter(self):
        """
        submit 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': f'issuetype = Bug AND status = Submitted AND assignee in ({self.all_users}) AND priority in (Critical,Major)AND createdDate >= startOfYear()',
                'source': 'zh',
            },
            {
                'jql_str': f'issuetype = Bug AND status = Submitted AND assignee in ({self.all_users}) AND priority in (Critical,Major)AND createdDate >= startOfYear()',
                'source': 'ex',
            }
        ]
        # result = self.format_bug_data(jql_str_list)
        result = self.format_bug_data_by_assignee(jql_str_list)
        return result

    def resolved_bug_filter(self):
        """
        Won't fix\Reject\平台限制问题处理 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': f"""issuetype = Bug AND status = Resolved AND assignee in ({self.all_users}) AND createdDate >= startOfYear() AND resolution not in (Duplicate,"Won't Fix","Platform Limit",Reject)""",
                'source': 'zh',
            },
            {
                'jql_str': f"""issuetype = Bug AND status = Resolved AND assignee in ({self.all_users}) AND createdDate >= startOfYear() AND resolution not in (Duplicate,"Won't Fix","Platform Limit",Reject)""",
                'source': 'ex',
            }
        ]
        result = self.format_bug_data_by_assignee(jql_str_list)
        return result

    def retest_bug_filter(self):
        """
        retest 问题审核提醒  取今年的数据
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': f'issuetype = Bug AND status != Closed  AND assignee in ({self.all_users}) AND  createdDate >= startOfYear() AND Tag = Retest',
                'source': 'zh',
            },
            {
                'jql_str': f'issuetype = Bug AND status != Closed  AND assignee in ({self.all_users}) AND  createdDate >= startOfYear() AND Tag = Retest',
                'source': 'ex',
            }
        ]

        result = self.format_bug_data_by_assignee(jql_str_list)
        return result

    def wont_fix_reject_bug_filter(self):
        """
        Won't fix\Reject\平台限制问题处理 问题审核提醒
        :param filter_url_list:
        :return:
        """
        jql_str_list = [
            {
                'jql_str': f"""issuetype = Bug AND status not in (Abandoned, Closed) AND assignee in ({self.all_users}) AND createdDate >= startOfYear() AND resolution in ("Platform Limit", Reject, "Won't Fix")""",
                'source': 'zh',
            },
            {
                'jql_str': f"""issuetype = Bug AND status not in (Abandoned, Closed) AND assignee in ({self.all_users}) AND createdDate >= startOfYear() AND resolution in ("Platform Limit", Reject, "Won't Fix")""",
                'source': 'ex',
            }
        ]
        result = self.format_bug_data_by_assignee(jql_str_list)
        return result

    def format_bug_notification(self, data: Dict, prompt='待审核问题', TMDE=False, notification_type='blocker'):
        tmp_data = {}
        for component, bug_data in data.items():
            if component not in self.component_owner:  # 不在当前定义模块中的数据，跳过
                continue
            if TMDE:
                assignee = self.component_owner[component]['TMDE']
            else:
                assignee = self.component_owner[component]['owner']
            if tmp_data.get(assignee):
                tmp_data[assignee] += bug_data
            else:
                tmp_data.update({
                    assignee: bug_data
                })
        if TMDE:
            prompt = prompt + '已超时，请及时通知owner处理'
        for assignee, bug_data in tmp_data.items():

            # print(assignee)
            tmp = '{"text":"%s：' % prompt
            for i in bug_data:
                # print(i)
                for key, value in i.items():
                    tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)

            # 记录推送统计数据
            try:
                receiver_type = 'tmde' if TMDE else 'assignee'
                self.stats_service.record_notification(
                    notification_type=notification_type,
                    receiver_type=receiver_type,
                    receiver_name=assignee,
                    bug_data=bug_data,
                    push_content=tmp,
                    push_success=True
                )
            except Exception as e:
                print(f"记录推送统计失败: {str(e)}")

            if assignee in ['王冬来', '仇玉婷', '屠新田', '冯思远', '宓玄玄', '田弟川', '陶书红', '卜昌义', '陈程']:
                print(tmp)
                # self.feishu_bot.send_bug_notification_v5(assignee, tmp)
    def format_bug_notification_tmde(self, data: Dict, prompt='待审核问题', TMDE=False, notification_type='blocker'):
        tmp_data = {}
        for component, bug_data in data.items():
            if component not in self.component_owner:  # 不在当前定义模块中的数据，跳过
                continue

            assignee = self.component_owner[component]['TMDE']

            if tmp_data.get(assignee):
                tmp_data[assignee][component] = bug_data
            else:
                tmp_data.update({
                    assignee:{component: bug_data}
                })
        prompt = prompt + '已超时，请及时通知owner处理'

        for assignee, component_bug_data in tmp_data.items():
            tmp = '{"text":"%s：' % prompt

            # 统计总Bug数量
            total_bugs = []
            for component, bug_data in component_bug_data.items():
                tmp += component + '：'
                total_bugs.extend(bug_data)
                for i in bug_data:
                    for key, value in i.items():
                        tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)

            # 记录推送统计数据
            try:
                self.stats_service.record_notification(
                    notification_type=notification_type,
                    receiver_type='tmde',
                    receiver_name=assignee,
                    tmde_name=assignee,
                    bug_data=total_bugs,
                    push_content=tmp,
                    push_success=True
                )
            except Exception as e:
                print(f"记录TMDE推送统计失败: {str(e)}")
            print(tmp)
            # if assignee in ['陈程']:
            #     print(tmp)
            #     self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            # self.feishu_bot.send_bug_notification_v5(assignee, tmp)
    def format_bug_notification_by_assignee(self, data: Dict, prompt='待审核问题', TMDE=False, notification_type='submitted'):
        for assignee, bug_data in data.items():
            # print(assignee)
            tmp = '{"text":"%s：' % prompt
            for i in bug_data:
                for key, value in i.items():
                    tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)

            # 记录推送统计数据
            try:
                self.stats_service.record_notification(
                    notification_type=notification_type,
                    receiver_type='assignee',
                    receiver_name=assignee,
                    receiver_username=assignee,
                    bug_data=bug_data,
                    push_content=tmp,
                    push_success=True
                )
            except Exception as e:
                print(f"记录推送统计失败: {str(e)}")
            print(tmp)
            # if assignee in ['donglai.wang', 'yuting.qiu', 'xintian.tu', 'siyuan.feng', 'xuanxuan.mi', 'dichuan.tian', 'shuhong.tao', 'changyi.bu', 'cheng.chen']:
            #     # print(tmp)
            #     self.feishu_bot.send_bug_notification_v6(assignee, tmp)
            # self.feishu_bot.send_bug_notification_v6(assignee, tmp)

    def format_bug_notification_by_assignee_tmde(self, data: Dict, prompt='待审核问题', TMDE=False, notification_type='submitted'):
        if TMDE:
            prompt = prompt + '已超时，请及时通知owner处理'

        for tmde, assignee_bug_data in data.items():
            # print(tmde)
            tmp = '{"text":"%s：' % prompt

            # 统计总Bug数量
            total_bugs = []
            for assignee, bug_data in assignee_bug_data.items():
                tmp += assignee + '：'
                total_bugs.extend(bug_data)
                for i in bug_data:
                    for key, value in i.items():
                        tmp += value['url'] + ' ' + '\\n' + ' '
            tmp = tmp + '"}'
            # print(tmp)

            # 记录推送统计数据
            try:
                self.stats_service.record_notification(
                    notification_type=notification_type,
                    receiver_type='tmde',
                    receiver_name=tmde,
                    receiver_username=tmde,
                    tmde_name=tmde,
                    bug_data=total_bugs,
                    push_content=tmp,
                    push_success=True
                )
            except Exception as e:
                print(f"记录TMDE推送统计失败: {str(e)}")

            # if tmde in ['cheng.chen']:
            #     # print(tmp)
            #     self.feishu_bot.send_bug_notification_v6(tmde, tmp)
            print(tmp)
            # self.feishu_bot.send_bug_notification_v6(tmde, tmp)

    def do_submitted_bug_notification(self):
        """
        1、Submitted（不包含A类），4点提醒（周一至周五）
        2、Retest，4点提醒（周一至周五）
        3、WontFix、Reject，4点提醒（周一至周五）
        :return:
        """
        # 获取到所以下午4点的提醒
        data = self.submit_bug_filter()
        # print(json.dumps(data, ensure_ascii=False))
        # 获取模块owner
        assignee_data = data['assignee_bugs']
        tmde_data = data['tmde_bugs']

        # 获取owner的所有提醒数据
        self.format_bug_notification_by_assignee(assignee_data, notification_type='submitted')

        # 获取TMDE的所有提醒数据
        self.format_bug_notification_by_assignee_tmde(tmde_data, TMDE=True, notification_type='submitted')

    def do_retest_bug_notification(self):
        """
        1、Submitted（不包含A类），4点提醒（周一至周五）
        2、Retest，4点提醒（周一至周五）
        3、WontFix、Reject，4点提醒（周一至周五）
        :return:
        """
        # 获取到所以下午4点的提醒
        data = self.retest_bug_filter()
        # 获取模块owner
        assignee_data = data['assignee_bugs']
        tmde_data = data['tmde_bugs']

        # 获取owner的所有提醒数据
        self.format_bug_notification_by_assignee(assignee_data, prompt='Retest问题', notification_type='retest')

        # 获取TMDE的所有提醒数据
        self.format_bug_notification_by_assignee_tmde(tmde_data, prompt='Retest问题', TMDE=True, notification_type='retest')

    def do_wont_fix_reject_bug_notification(self):
        """
        1、Submitted（不包含A类），4点提醒（周一至周五）
        2、Retest，4点提醒（周一至周五）
        3、WontFix、Reject，4点提醒（周一至周五）
        :return:
        """
        # 获取到所以下午4点的提醒
        data = self.wont_fix_reject_bug_filter()
        # 获取模块owner
        assignee_data = data['assignee_bugs']
        tmde_data = data['tmde_bugs']

        # 获取owner的所有提醒数据
        self.format_bug_notification_by_assignee(assignee_data, prompt='WontFix、Reject', notification_type='wont_fix_reject')

        # 获取TMDE的所有提醒数据
        self.format_bug_notification_by_assignee_tmde(tmde_data, prompt='WontFix、Reject', TMDE=True, notification_type='wont_fix_reject')

    def do_resolved_bug_notification(self):
        """
        1、Submitted（不包含A类），4点提醒（周一至周五）
        2、Retest，4点提醒（周一至周五）
        3、WontFix、Reject，4点提醒（周一至周五）
        :return:
        """
        # 获取到所以下午4点的提醒
        data = self.resolved_bug_filter()
        # 获取模块owner
        assignee_data = data['assignee_bugs']
        tmde_data = data['tmde_bugs']

        # 获取owner的所有提醒数据
        self.format_bug_notification_by_assignee(assignee_data, prompt='Resolved切VF', notification_type='resolved')

        # 获取TMDE的所有提醒数据
        self.format_bug_notification_by_assignee_tmde(tmde_data, prompt='Resolved切VF', TMDE=True, notification_type='resolved')

    def do_blocker_bug_notification(self):
        """
        1、Submitted（不包含A类），4点提醒（周一至周五）
        2、Retest，4点提醒（周一至周五）
        3、WontFix、Reject，4点提醒（周一至周五）
        :return:
        """
        # 获取到所以下午4点的提醒
        data = self.blocker_bug_filter()
        # 获取模块owner
        assignee_data = data['assignee']
        tmde_data = data['tmde']

        # 获取owner的所有提醒数据
        self.format_bug_notification(assignee_data, prompt='A类问题审核', notification_type='blocker')

        # 获取TMDE的所有提醒数据
        self.format_bug_notification_tmde(tmde_data, prompt='A类问题审核', TMDE=True, notification_type='blocker')


if __name__ == '__main__':
    demo = JiraTools(
        jira_url="http://jira.transsion.com/",
        jira_user="changyi.bu",
        jira_password="Zdd&88329",
    )

    # demo.do_submitted_bug_notification()
    # demo.do_retest_bug_notification()
    # demo.do_wont_fix_reject_bug_notification()
    # demo.do_resolved_bug_notification()
    demo.do_blocker_bug_notification()
    # print(demo.submit_bug_filter())
